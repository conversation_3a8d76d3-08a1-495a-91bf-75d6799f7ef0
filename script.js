// Gold text effect JavaScript functionality

let animationPaused = false;
let textIndex = 0;

// Array of different text options
const goldTexts = [
    'GOLD EFFECT',
    'LUXURY',
    'PREMIUM',
    'GOLDEN',
    'TREASURE',
    'ROYAL',
    'ELITE'
];

const animatedTexts = [
    'ANIMATED GOLD',
    'SHIMMERING',
    'GLOWING',
    'BRILLIANT',
    'RADIANT',
    'LUSTROUS',
    'GLEAMING'
];

// Toggle animation function
function toggleAnimation() {
    const animatedElement = document.getElementById('animatedGold');
    animationPaused = !animationPaused;
    
    if (animationPaused) {
        animatedElement.classList.add('paused');
        document.querySelector('button').textContent = 'Resume Animation';
    } else {
        animatedElement.classList.remove('paused');
        document.querySelector('button').textContent = 'Pause Animation';
    }
}

// Change text function
function changeText() {
    textIndex = (textIndex + 1) % goldTexts.length;
    
    const staticElement = document.getElementById('goldText');
    const animatedElement = document.getElementById('animatedGold');
    
    // Add fade out effect
    staticElement.style.opacity = '0';
    animatedElement.style.opacity = '0';
    
    setTimeout(() => {
        staticElement.textContent = goldTexts[textIndex];
        animatedElement.textContent = animatedTexts[textIndex];

        // Update data attributes for pseudo-elements
        staticElement.setAttribute('data-text', goldTexts[textIndex]);
        animatedElement.setAttribute('data-text', animatedTexts[textIndex]);

        // Fade back in
        staticElement.style.opacity = '1';
        animatedElement.style.opacity = '1';
    }, 300);
}

// Add smooth transitions for text changes and set up data attributes
document.addEventListener('DOMContentLoaded', function() {
    const goldElements = document.querySelectorAll('.gold-text, .gold-text-animated');

    goldElements.forEach(element => {
        element.style.transition = 'opacity 0.3s ease-in-out';
        // Set data attribute for pseudo-elements
        element.setAttribute('data-text', element.textContent);
    });
    
    // Add hover effects
    goldElements.forEach(element => {
        element.addEventListener('mouseenter', function() {
            this.style.transform = 'perspective(500px) rotateX(15deg) scale(1.05)';
        });
        
        element.addEventListener('mouseleave', function() {
            if (this.classList.contains('gold-text')) {
                this.style.transform = 'perspective(500px) rotateX(15deg) scale(1)';
            } else {
                this.style.transform = 'perspective(500px) rotateX(10deg) scale(1)';
            }
        });
    });
});

// Optional: Add particle effect for extra glamour
function createGoldParticles() {
    const container = document.querySelector('.container');
    
    for (let i = 0; i < 20; i++) {
        const particle = document.createElement('div');
        particle.className = 'gold-particle';
        particle.style.cssText = `
            position: absolute;
            width: 4px;
            height: 4px;
            background: radial-gradient(circle, #ffd700, #b8860b);
            border-radius: 50%;
            pointer-events: none;
            opacity: 0;
            left: ${Math.random() * 100}%;
            top: ${Math.random() * 100}%;
            animation: floatParticle ${3 + Math.random() * 4}s ease-in-out infinite;
            animation-delay: ${Math.random() * 2}s;
        `;
        
        container.appendChild(particle);
    }
}

// Particle animation keyframes (added via JavaScript)
const style = document.createElement('style');
style.textContent = `
    @keyframes floatParticle {
        0%, 100% {
            opacity: 0;
            transform: translateY(0px) scale(0);
        }
        50% {
            opacity: 0.8;
            transform: translateY(-20px) scale(1);
        }
    }
`;
document.head.appendChild(style);

// Initialize particles on load
document.addEventListener('DOMContentLoaded', function() {
    // Uncomment the line below to enable floating particles
    // createGoldParticles();
});

// Update button text on initial load
document.addEventListener('DOMContentLoaded', function() {
    const toggleButton = document.querySelector('button');
    toggleButton.textContent = 'Pause Animation';
});
