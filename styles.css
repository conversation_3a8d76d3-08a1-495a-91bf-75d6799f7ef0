@import url('https://fonts.googleapis.com/css2?family=Cinzel:wght@400;600;700&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: 'Cinzel', serif;
    overflow-x: hidden;
}

.container {
    text-align: center;
    padding: 2rem;
}

/* Static Gold Text Effect */
.gold-text {
    font-size: clamp(3rem, 8vw, 8rem);
    font-weight: 700;
    letter-spacing: 0.1em;
    margin: 2rem 0;
    
    /* Gold gradient */
    background: linear-gradient(
        45deg,
        #b8860b 0%,
        #ffd700 25%,
        #ffed4e 50%,
        #ffd700 75%,
        #b8860b 100%
    );
    background-size: 200% 200%;
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    
    /* Multiple text shadows for depth */
    text-shadow: 
        0 0 10px rgba(255, 215, 0, 0.8),
        0 0 20px rgba(255, 215, 0, 0.6),
        0 0 30px rgba(255, 215, 0, 0.4),
        2px 2px 4px rgba(0, 0, 0, 0.8),
        4px 4px 8px rgba(0, 0, 0, 0.6);
    
    /* 3D effect */
    position: relative;
    transform: perspective(500px) rotateX(15deg);
}

/* Animated Gold Text Effect */
.gold-text-animated {
    font-size: clamp(2.5rem, 6vw, 6rem);
    font-weight: 600;
    letter-spacing: 0.1em;
    margin: 2rem 0;
    
    /* Animated gold gradient */
    background: linear-gradient(
        45deg,
        #b8860b 0%,
        #ffd700 20%,
        #ffed4e 40%,
        #fff700 60%,
        #ffd700 80%,
        #b8860b 100%
    );
    background-size: 300% 300%;
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    
    /* Glowing animation */
    animation: goldShimmer 3s ease-in-out infinite,
               goldGlow 2s ease-in-out infinite alternate;
    
    /* Enhanced shadows */
    text-shadow: 
        0 0 15px rgba(255, 215, 0, 0.9),
        0 0 25px rgba(255, 215, 0, 0.7),
        0 0 35px rgba(255, 215, 0, 0.5),
        3px 3px 6px rgba(0, 0, 0, 0.9),
        6px 6px 12px rgba(0, 0, 0, 0.7);
    
    position: relative;
    transform: perspective(500px) rotateX(10deg);
}

/* Shimmer animation */
@keyframes goldShimmer {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

/* Glow pulse animation */
@keyframes goldGlow {
    0% {
        text-shadow: 
            0 0 15px rgba(255, 215, 0, 0.9),
            0 0 25px rgba(255, 215, 0, 0.7),
            0 0 35px rgba(255, 215, 0, 0.5),
            3px 3px 6px rgba(0, 0, 0, 0.9),
            6px 6px 12px rgba(0, 0, 0, 0.7);
    }
    100% {
        text-shadow: 
            0 0 25px rgba(255, 215, 0, 1),
            0 0 35px rgba(255, 215, 0, 0.9),
            0 0 45px rgba(255, 215, 0, 0.7),
            3px 3px 6px rgba(0, 0, 0, 0.9),
            6px 6px 12px rgba(0, 0, 0, 0.7);
    }
}

/* Controls */
.controls {
    margin-top: 3rem;
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

button {
    padding: 0.8rem 1.5rem;
    background: linear-gradient(45deg, #b8860b, #ffd700);
    border: none;
    border-radius: 25px;
    color: #1a1a1a;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 215, 0, 0.5);
}

button:active {
    transform: translateY(0);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .container {
        padding: 1rem;
    }
    
    .controls {
        flex-direction: column;
        align-items: center;
    }
    
    button {
        width: 200px;
    }
}

/* Animation pause class */
.paused {
    animation-play-state: paused !important;
}
