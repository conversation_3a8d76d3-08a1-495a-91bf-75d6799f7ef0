@import url('https://fonts.googleapis.com/css2?family=Cinzel:wght@400;600;700&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    /* Realistic background - like a museum or jewelry display */
    background:
        radial-gradient(ellipse at 30% 20%, #2c2c2c 0%, #1a1a1a 70%),
        linear-gradient(135deg, #1e1e1e 0%, #0f0f0f 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: 'Cinzel', serif;
    overflow-x: hidden;
    position: relative;
}

/* Subtle directional lighting effect - like museum spotlights */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(ellipse 800px 600px at 30% 20%, rgba(184, 134, 11, 0.03) 0%, transparent 60%);
    pointer-events: none;
    z-index: -1;
}

.container {
    text-align: center;
    padding: 2rem;
}

/* Realistic Gold Text Effect - Like Real Gold Metal */
.gold-text {
    font-size: clamp(3rem, 8vw, 8rem);
    font-weight: 700;
    letter-spacing: 0.05em;
    margin: 2rem 0;
    position: relative;

    /* Authentic gold base color - real gold is more muted */
    color: #b8860b;

    /* Realistic gold gradient - simulating directional lighting on real gold */
    background: linear-gradient(
        145deg,
        #6b5b0f 0%,      /* Deep shadow areas */
        #8b6914 20%,     /* Shadow transition */
        #b8860b 35%,     /* Main gold color */
        #d4af37 50%,     /* Lit areas */
        #e6c547 65%,     /* Bright reflection */
        #d4af37 80%,     /* Return to main */
        #8b6914 100%     /* Edge shadows */
    );
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;

    /* Realistic shadows - as if lit from upper left */
    text-shadow:
        /* Main cast shadow */
        3px 3px 0px #5d4e0a,
        4px 4px 0px #4a3f08,
        5px 5px 0px #3d3306,
        6px 6px 8px rgba(0, 0, 0, 0.6),
        8px 8px 16px rgba(0, 0, 0, 0.3),
        /* Subtle highlight on top edge */
        -1px -1px 0px rgba(230, 197, 71, 0.3);

    /* Add surface texture simulation */
    background-image:
        /* Subtle noise pattern to simulate gold texture */
        radial-gradient(circle at 20% 80%, rgba(230, 197, 71, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(139, 105, 20, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(212, 175, 55, 0.05) 0%, transparent 50%);
}

/* Realistic Animated Gold Text Effect - Subtle Light Movement */
.gold-text-animated {
    font-size: clamp(2.5rem, 6vw, 6rem);
    font-weight: 600;
    letter-spacing: 0.05em;
    margin: 2rem 0;
    position: relative;

    /* Authentic gold base color */
    color: #b8860b;

    /* Realistic gold with subtle light movement */
    background: linear-gradient(
        120deg,
        #5d4e0a 0%,      /* Deep shadows */
        #6b5b0f 15%,     /* Shadow areas */
        #8b6914 30%,     /* Transition */
        #b8860b 45%,     /* Main gold */
        #d4af37 55%,     /* Lit surface */
        #e6c547 60%,     /* Highlight */
        #d4af37 70%,     /* Return */
        #b8860b 85%,     /* Main gold */
        #6b5b0f 100%     /* Edge shadow */
    );
    background-size: 200% 100%;
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;

    /* Subtle realistic animation - like light slowly moving across gold */
    animation: realGoldLight 8s ease-in-out infinite;

    /* Realistic shadows - no glow, just proper shadow casting */
    text-shadow:
        /* Main cast shadow */
        3px 3px 0px #5d4e0a,
        4px 4px 0px #4a3f08,
        5px 5px 0px #3d3306,
        6px 6px 10px rgba(0, 0, 0, 0.7),
        10px 10px 20px rgba(0, 0, 0, 0.4),
        /* Very subtle top highlight */
        -1px -1px 0px rgba(230, 197, 71, 0.2);

    /* Surface texture like real gold */
    background-image:
        radial-gradient(circle at 30% 70%, rgba(230, 197, 71, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 70% 30%, rgba(139, 105, 20, 0.08) 0%, transparent 50%);
}

/* Realistic gold surface imperfections and depth */
.gold-text::before,
.gold-text-animated::before {
    content: attr(data-text);
    position: absolute;
    top: -1px;
    left: -1px;
    z-index: -1;

    /* Very subtle highlight edge - like real gold edge catching light */
    color: rgba(230, 197, 71, 0.4);
    text-shadow: none;
    -webkit-text-stroke: 1px rgba(230, 197, 71, 0.2);
}

.gold-text::after,
.gold-text-animated::after {
    content: attr(data-text);
    position: absolute;
    top: 1px;
    left: 1px;
    z-index: -2;

    /* Deeper shadow base - like real carved gold */
    color: #3d3306;
    text-shadow:
        1px 1px 0px #2a2404,
        2px 2px 0px #1a1602;
}

/* Realistic Gold Light Movement - Like Real Studio Lighting */
@keyframes realGoldLight {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

/* Controls */
.controls {
    margin-top: 3rem;
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

button {
    padding: 0.8rem 1.5rem;
    background: linear-gradient(45deg, #b8860b, #ffd700);
    border: none;
    border-radius: 25px;
    color: #1a1a1a;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 215, 0, 0.5);
}

button:active {
    transform: translateY(0);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .container {
        padding: 1rem;
    }
    
    .controls {
        flex-direction: column;
        align-items: center;
    }
    
    button {
        width: 200px;
    }
}

/* Animation pause class */
.paused {
    animation-play-state: paused !important;
}
