@import url('https://fonts.googleapis.com/css2?family=Cinzel:wght@400;600;700&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    background:
        radial-gradient(ellipse at center, #2a2a2a 0%, #1a1a1a 50%, #0a0a0a 100%),
        linear-gradient(45deg, #1a1a1a 0%, #2d2d2d 50%, #1a1a1a 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: 'Cinzel', serif;
    overflow-x: hidden;
    position: relative;
}

/* Add subtle texture to background */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(255, 215, 0, 0.02) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(255, 215, 0, 0.02) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

.container {
    text-align: center;
    padding: 2rem;
}

/* Realistic Gold Text Effect */
.gold-text {
    font-size: clamp(3rem, 8vw, 8rem);
    font-weight: 700;
    letter-spacing: 0.05em;
    margin: 2rem 0;
    position: relative;

    /* Base gold color */
    color: #d4af37;

    /* Complex realistic gold gradient with metallic reflections */
    background: linear-gradient(
        135deg,
        #8b6914 0%,
        #b8860b 15%,
        #daa520 25%,
        #ffd700 35%,
        #ffed4a 45%,
        #fff8dc 50%,
        #ffed4a 55%,
        #ffd700 65%,
        #daa520 75%,
        #b8860b 85%,
        #8b6914 100%
    );
    background-size: 200% 200%;
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;

    /* Realistic metallic shadows and highlights */
    text-shadow:
        /* Inner shadow for depth */
        inset 0 1px 0 rgba(255, 255, 255, 0.4),
        /* Main shadow */
        0 1px 3px rgba(0, 0, 0, 0.8),
        0 2px 6px rgba(0, 0, 0, 0.6),
        0 4px 12px rgba(0, 0, 0, 0.4),
        /* Gold glow */
        0 0 20px rgba(212, 175, 55, 0.6),
        0 0 40px rgba(212, 175, 55, 0.3),
        /* Highlight reflection */
        0 -1px 0 rgba(255, 255, 255, 0.2);

    /* 3D beveled effect */
    filter:
        drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.5))
        drop-shadow(0 0 10px rgba(255, 215, 0, 0.3));
}

/* Realistic Animated Gold Text Effect */
.gold-text-animated {
    font-size: clamp(2.5rem, 6vw, 6rem);
    font-weight: 600;
    letter-spacing: 0.05em;
    margin: 2rem 0;
    position: relative;

    /* Base gold color */
    color: #d4af37;

    /* Ultra-realistic animated gold gradient with light reflections */
    background: linear-gradient(
        110deg,
        #704214 0%,
        #8b6914 8%,
        #b8860b 16%,
        #daa520 24%,
        #ffd700 32%,
        #ffed4a 40%,
        #fff8dc 48%,
        #ffffff 50%,
        #fff8dc 52%,
        #ffed4a 60%,
        #ffd700 68%,
        #daa520 76%,
        #b8860b 84%,
        #8b6914 92%,
        #704214 100%
    );
    background-size: 300% 300%;
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;

    /* Realistic metallic animation */
    animation:
        goldReflection 4s ease-in-out infinite,
        goldPulse 3s ease-in-out infinite alternate,
        goldShine 6s linear infinite;

    /* Complex realistic shadows */
    text-shadow:
        /* Inner highlights */
        0 1px 0 rgba(255, 255, 255, 0.3),
        0 -1px 0 rgba(0, 0, 0, 0.7),
        /* Depth shadows */
        1px 1px 2px rgba(0, 0, 0, 0.8),
        2px 2px 4px rgba(0, 0, 0, 0.6),
        4px 4px 8px rgba(0, 0, 0, 0.4),
        8px 8px 16px rgba(0, 0, 0, 0.2),
        /* Gold glow */
        0 0 20px rgba(212, 175, 55, 0.8),
        0 0 40px rgba(212, 175, 55, 0.4),
        0 0 60px rgba(212, 175, 55, 0.2);

    /* Enhanced 3D effect */
    filter:
        drop-shadow(3px 3px 6px rgba(0, 0, 0, 0.6))
        drop-shadow(0 0 15px rgba(255, 215, 0, 0.4))
        contrast(1.2)
        brightness(1.1);
}

/* Add realistic metallic highlights using pseudo-elements */
.gold-text::before,
.gold-text-animated::before {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;

    /* Highlight layer */
    background: linear-gradient(
        135deg,
        transparent 0%,
        rgba(255, 255, 255, 0.3) 45%,
        rgba(255, 255, 255, 0.8) 50%,
        rgba(255, 255, 255, 0.3) 55%,
        transparent 100%
    );
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;

    /* Subtle animation for highlights */
    animation: highlightShift 8s ease-in-out infinite;
}

.gold-text::after,
.gold-text-animated::after {
    content: attr(data-text);
    position: absolute;
    top: 2px;
    left: 2px;
    z-index: -2;

    /* Shadow layer for more depth */
    color: rgba(139, 105, 20, 0.8);
    text-shadow: none;
    filter: blur(1px);
}

/* Highlight animation */
@keyframes highlightShift {
    0%, 100% {
        background-position: -200% 0%;
    }
    50% {
        background-position: 200% 0%;
    }
}

/* Realistic Gold Reflection Animation */
@keyframes goldReflection {
    0% {
        background-position: -200% 0%;
    }
    50% {
        background-position: 200% 0%;
    }
    100% {
        background-position: -200% 0%;
    }
}

/* Metallic Shine Animation */
@keyframes goldShine {
    0% {
        background-position: -300% 0%;
    }
    100% {
        background-position: 300% 0%;
    }
}

/* Realistic Gold Pulse */
@keyframes goldPulse {
    0% {
        filter:
            drop-shadow(3px 3px 6px rgba(0, 0, 0, 0.6))
            drop-shadow(0 0 15px rgba(255, 215, 0, 0.4))
            contrast(1.2)
            brightness(1.1);
    }
    100% {
        filter:
            drop-shadow(4px 4px 8px rgba(0, 0, 0, 0.7))
            drop-shadow(0 0 25px rgba(255, 215, 0, 0.6))
            contrast(1.3)
            brightness(1.2);
    }
}

/* Controls */
.controls {
    margin-top: 3rem;
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

button {
    padding: 0.8rem 1.5rem;
    background: linear-gradient(45deg, #b8860b, #ffd700);
    border: none;
    border-radius: 25px;
    color: #1a1a1a;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 215, 0, 0.5);
}

button:active {
    transform: translateY(0);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .container {
        padding: 1rem;
    }
    
    .controls {
        flex-direction: column;
        align-items: center;
    }
    
    button {
        width: 200px;
    }
}

/* Animation pause class */
.paused {
    animation-play-state: paused !important;
}
